const express = require('express');
const { getDB } = require('../config/db');
const { verifyAuthenticateToken } = require('../auth');

const router = express.Router();

// Protect all routes with authentication
router.use(verifyAuthenticateToken);

/**
 * GET /api/users - Retrieve all users
 */
router.get('/', async (req, res, next) => {
  try {
    const db = getDB();
    const users = await db.collection('users').find({}).toArray();
    res.status(200).json(users);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/users/:id - Retrieve a single user by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();
    const user = await db.collection('users').findOne({ _id: id });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.status(200).json(user);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/users - Create a new user
 */
router.post('/', async (req, res, next) => {
  try {
    const { username, email, displayName } = req.body;

    if (!username || !email) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'username and email are required fields',
      });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid email format',
      });
    }

    const db = getDB();

    const existingUser = await db.collection('users').findOne({
      $or: [{ username }, { email }],
    });

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'A user with this username or email already exists',
      });
    }

    const newUser = {
      username,
      email,
      displayName: displayName || username,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.collection('users').insertOne(newUser);
    const createdUser = await db
      .collection('users')
      .findOne({ _id: result.insertedId });

    res.status(201).json(createdUser);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/users/:id - Update an existing user
 */
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { username, email, displayName } = req.body;

    if (!username && !email && !displayName) {
      return res.status(400).json({
        error: 'Validation failed',
        message:
          'At least one field (username, email, or displayName) must be provided',
      });
    }

    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({
          error: 'Validation failed',
          message: 'Invalid email format',
        });
      }
    }

    const db = getDB();

    const existingUser = await db.collection('users').findOne({ _id: id });
    if (!existingUser) {
      return res.status(404).json({ error: 'User not found' });
    }

    if (username || email) {
      const duplicateUser = await db.collection('users').findOne({
        _id: { $ne: id },
        $or: [
          ...(username ? [{ username }] : []),
          ...(email ? [{ email }] : []),
        ],
      });

      if (duplicateUser) {
        return res.status(409).json({
          error: 'Conflict',
          message: 'A user with this username or email already exists',
        });
      }
    }

    const updateData = {
      ...(username && { username }),
      ...(email && { email }),
      ...(displayName && { displayName }),
      updatedAt: new Date(),
    };

    await db.collection('users').updateOne({ _id: id }, { $set: updateData });

    const updatedUser = await db.collection('users').findOne({ _id: id });
    res.status(200).json(updatedUser);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/users/:id - Delete a user
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();
    const result = await db.collection('users').deleteOne({ _id: id });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'User not found' });
    }

    res.status(200).json({
      message: 'User deleted successfully',
      deletedId: id,
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
