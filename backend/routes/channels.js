const express = require('express');
const { getDB } = require('../config/db');
const { verifyAuthenticateToken } = require('../auth');

const router = express.Router();

// Protect all routes with authentication
router.use(verifyAuthenticateToken);

/**
 * GET /api/channels - Retrieve all channels
 */
router.get('/', async (req, res, next) => {
  try {
    const db = getDB();
    const channels = await db
      .collection('channels')
      .find({})
      .sort({ createdAt: -1 })
      .toArray();

    res.status(200).json(channels);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/channels/:id/join - Join a channel
 */
router.post('/:id/join', async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user?.userId;
    console.log('Joining channel', id, 'as user', userId);

    if (!userId) {
      return res.status(401).json({ error: 'User not authenticated' });
    }

    const db = getDB();

    const channel = await db.collection('channels').findOne({ _id: id });
    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    // Check if user is already a member
    if (channel.memberIds && channel.memberIds.includes(userId)) {
      return res.status(400).json({
        error: 'Already a member',
        message: 'You are already a member of this channel',
      });
    }

    // Add user to channel members
    const updatedMemberIds = [...(channel.memberIds || []), userId];
    await db.collection('channels').updateOne(
      { _id: id },
      {
        $set: {
          memberIds: updatedMemberIds,
          memberCount: updatedMemberIds.length,
          updatedAt: new Date(),
        },
      }
    );

    const updatedChannel = await db.collection('channels').findOne({ _id: id });
    res.status(200).json(updatedChannel);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/channels/:id - Retrieve a single channel by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();
    const channel = await db.collection('channels').findOne({ _id: id });

    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    res.status(200).json(channel);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/channels - Create a new channel
 */
router.post('/', async (req, res, next) => {
  try {
    const { name, description, isPrivate, userIds } = req.body;

    if (!name) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'name is a required field',
      });
    }

    if (name.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Channel name cannot be empty',
      });
    }

    if (name.length > 50) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Channel name cannot exceed 50 characters',
      });
    }

    const db = getDB();

    const existingChannel = await db.collection('channels').findOne({
      name: name.trim(),
    });

    if (existingChannel) {
      return res.status(409).json({
        error: 'Channel already exists',
        message: 'A channel with this name already exists',
      });
    }

    // Build member list: include authenticated user and any additional userIds
    let memberIds = [];

    // Always include the authenticated user (channel creator)
    if (req.user && req.user.userId) {
      memberIds.push(req.user.userId);
    }

    if (userIds && Array.isArray(userIds)) {
      // Validate that all user IDs exist
      for (const userId of userIds) {
        const user = await db.collection('users').findOne({ _id: userId });
        if (!user) {
          return res.status(400).json({
            error: 'Validation failed',
            message: `User with ID ${userId} not found`,
          });
        }
        // Add current user if not already in the list
        if (!memberIds.includes(userId)) {
          memberIds.push(userId);
        }
      }
    }

    const newChannel = {
      _id: `channel-${Date.now()}`,
      name: name.trim(),
      description: description ? description.trim() : '',
      isPrivate: isPrivate === true,
      memberIds: memberIds,
      memberCount: memberIds.length,
      createdBy: req.user?.userId || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.collection('channels').insertOne(newChannel);
    const createdChannel = await db
      .collection('channels')
      .findOne({ _id: result.insertedId });

    res.status(201).json(createdChannel);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/channels/:id - Update an existing channel
 */
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, description, isPrivate } = req.body;

    if (!name && description === undefined && isPrivate === undefined) {
      return res.status(400).json({
        error: 'Validation failed',
        message:
          'At least one field (name, description, or isPrivate) must be provided',
      });
    }

    if (name !== undefined) {
      if (name.trim().length === 0) {
        return res.status(400).json({
          error: 'Validation failed',
          message: 'Channel name cannot be empty',
        });
      }

      if (name.length > 50) {
        return res.status(400).json({
          error: 'Validation failed',
          message: 'Channel name cannot exceed 50 characters',
        });
      }
    }

    const db = getDB();

    const existingChannel = await db
      .collection('channels')
      .findOne({ _id: id });
    if (!existingChannel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    if (name) {
      const duplicateChannel = await db.collection('channels').findOne({
        _id: { $ne: id },
        name: name.trim(),
      });

      if (duplicateChannel) {
        return res.status(409).json({
          error: 'Conflict',
          message: 'A channel with this name already exists',
        });
      }
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(description !== undefined && { description: description.trim() }),
      ...(isPrivate !== undefined && { isPrivate: isPrivate === true }),
      updatedAt: new Date(),
    };

    await db
      .collection('channels')
      .updateOne({ _id: id }, { $set: updateData });

    const updatedChannel = await db.collection('channels').findOne({ _id: id });
    res.status(200).json(updatedChannel);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/channels/:id - Delete a channel
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();

    await db.collection('messages').deleteMany({ channelId: id });

    const result = await db.collection('channels').deleteOne({ _id: id });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    res.status(200).json({
      message: 'Channel deleted successfully',
      deletedId: id,
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
