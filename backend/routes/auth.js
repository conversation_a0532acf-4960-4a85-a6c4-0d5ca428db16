const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { getDB } = require('../config/db');
const { verifyAuthenticateToken } = require('../auth');

const router = express.Router();

/**
 * POST /api/auth/register - Register a new user
 */
router.post('/register', async (req, res, next) => {
  try {
    const { username, email, password, displayName } = req.body;

    if (!username || !email || !password) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'username, email, and password are required',
      });
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Invalid email format',
      });
    }

    const db = getDB();

    const existingUser = await db.collection('users').findOne({
      $or: [{ username }, { email }],
    });

    if (existingUser) {
      return res.status(409).json({
        error: 'User already exists',
        message: 'A user with this username or email already exists',
      });
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const newUser = {
      _id: `user-${Date.now()}`,
      username,
      email,
      password: hashedPassword,
      displayName: displayName || username,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.collection('users').insertOne(newUser);
    const createdUser = await db
      .collection('users')
      .findOne({ _id: result.insertedId });

    const token = jwt.sign(
      { userId: createdUser._id, username: createdUser.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const { password: _, ...userWithoutPassword } = createdUser;

    res.status(201).json({
      user: userWithoutPassword,
      token,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/auth/login - Login user
 */
router.post('/login', async (req, res, next) => {
  try {
    const { username, email, password } = req.body;

    if (!password || (!username && !email)) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'password and either username or email are required',
      });
    }

    const db = getDB();

    const user = await db.collection('users').findOne({
      $or: [...(username ? [{ username }] : []), ...(email ? [{ email }] : [])],
    });

    if (!user) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Username/email or password is incorrect',
      });
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);

    if (!isPasswordValid) {
      return res.status(401).json({
        error: 'Invalid credentials',
        message: 'Username/email or password is incorrect',
      });
    }

    const token = jwt.sign(
      { userId: user._id, username: user.username },
      process.env.JWT_SECRET,
      { expiresIn: process.env.JWT_EXPIRES_IN }
    );

    const { password: _, ...userWithoutPassword } = user;

    res.status(200).json({
      user: userWithoutPassword,
      token,
    });
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/auth/me - Get current user (protected route)
 */
router.get('/me', verifyAuthenticateToken, async (req, res, next) => {
  try {
    const db = getDB();
    const user = await db.collection('users').findOne({ _id: req.user.userId });

    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const { password: _, ...userWithoutPassword } = user;

    res.status(200).json(userWithoutPassword);
  } catch (error) {
    next(error);
  }
});

module.exports = router;
