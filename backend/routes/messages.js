const express = require('express');
const { getDB } = require('../config/db');
const { verifyAuthenticateToken } = require('../auth');

const router = express.Router();

// Protect all routes with authentication
router.use(verifyAuthenticateToken);

/**
 * GET /api/messages - Retrieve all messages or filter by channelId
 * Query params: channelId (optional)
 */
router.get('/', async (req, res, next) => {
  try {
    const { channelId } = req.query;
    const db = getDB();

    let query = { channelId };

    const messages = await db
      .collection('messages')
      .find(query)
      .sort({ createdAt: -1 })
      .toArray();

    res.status(200).json(messages);
  } catch (error) {
    next(error);
  }
});

/**
 * GET /api/messages/:id - Retrieve a single message by ID
 */
router.get('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();
    const message = await db.collection('messages').findOne({ _id: id });

    if (!message) {
      return res.status(404).json({ error: 'Message not found' });
    }

    res.status(200).json(message);
  } catch (error) {
    next(error);
  }
});

/**
 * POST /api/messages - Create a new message
 */
router.post('/', async (req, res, next) => {
  try {
    const { content, senderId, channelId } = req.body;

    if (!content || !senderId || !channelId) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'content, senderId, and channelId are required fields',
      });
    }

    if (content.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Message content cannot be empty',
      });
    }

    const db = getDB();

    const user = await db.collection('users').findOne({ _id: senderId });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const channel = await db.collection('channels').findOne({ _id: channelId });
    if (!channel) {
      return res.status(404).json({ error: 'Channel not found' });
    }

    const newMessage = {
      _id: `msg-${Date.now()}`,
      content: content.trim(),
      senderId,
      channelId,
      timestamp: (new Date()).toISOString().split('.')[0] + 'Z'
    };

    const result = await db.collection('messages').insertOne(newMessage);
    const createdMessage = await db
      .collection('messages')
      .findOne({ _id: result.insertedId });

    res.status(201).json(createdMessage);
  } catch (error) {
    next(error);
  }
});

/**
 * PUT /api/messages/:id - Update an existing message
 */
router.put('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;
    const { content } = req.body;

    if (!content) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'content field is required',
      });
    }

    if (content.trim().length === 0) {
      return res.status(400).json({
        error: 'Validation failed',
        message: 'Message content cannot be empty',
      });
    }

    const db = getDB();

    const existingMessage = await db
      .collection('messages')
      .findOne({ _id: id });
    if (!existingMessage) {
      return res.status(404).json({ error: 'Message not found' });
    }

    const updateData = {
      content: content.trim(),
      updatedAt: new Date(),
      edited: true,
    };

    await db
      .collection('messages')
      .updateOne({ _id: id }, { $set: updateData });

    const updatedMessage = await db.collection('messages').findOne({ _id: id });
    res.status(200).json(updatedMessage);
  } catch (error) {
    next(error);
  }
});

/**
 * DELETE /api/messages/:id - Delete a message
 */
router.delete('/:id', async (req, res, next) => {
  try {
    const { id } = req.params;

    const db = getDB();
    const result = await db.collection('messages').deleteOne({ _id: id });

    if (result.deletedCount === 0) {
      return res.status(404).json({ error: 'Message not found' });
    }

    res.status(200).json({
      message: 'Message deleted successfully',
      deletedId: id,
    });
  } catch (error) {
    next(error);
  }
});

module.exports = router;
