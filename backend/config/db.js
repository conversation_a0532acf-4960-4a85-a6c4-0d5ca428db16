const { MongoClient } = require('mongodb');
require('dotenv').config();

let db = null;
let client = null;

/**
 * Connect to MongoDB database
 * @returns {Promise<Object>} Database instance
 */
async function connectDB() {
  if (db) {
    return db;
  }

  try {
    const uri =
      process.env.MONGODB_URI || 'mongodb://localhost:27017/chatterbox';
    client = new MongoClient(uri);

    await client.connect();
    console.log('✅ Connected to MongoDB successfully');

    // Extract database name from URI or use default
    const dbName = uri.split('/').pop().split('?')[0] || 'chatterbox';
    db = client.db(dbName);

    return db;
  } catch (error) {
    console.error('❌ MongoDB connection error:', error.message);
    throw error;
  }
}

/**
 * Get the database instance
 * @returns {Object} Database instance
 */
function getDB() {
  if (!db) {
    throw new Error('Database not initialized. Call connectDB() first.');
  }
  return db;
}

/**
 * Close database connection
 */
async function closeDB() {
  if (client) {
    await client.close();
    db = null;
    client = null;
    console.log('MongoDB connection closed');
  }
}

module.exports = {
  connectDB,
  getDB,
  closeDB,
};
