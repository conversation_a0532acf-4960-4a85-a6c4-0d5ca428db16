# Dependencies
node_modules

# Build outputs
dist
build
.vite

# Coverage
coverage

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Environment files
.env
.env.local
.env.*.local

# IDE
.vscode
.idea
*.swp
*.swo
*~

# OS
.DS_Store
Thumbs.db

# Package manager lock files (optional - uncomment if you don't want to format these)
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# Other
public

