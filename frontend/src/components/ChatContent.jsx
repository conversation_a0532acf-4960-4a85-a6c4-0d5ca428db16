import { useEffect, useState } from 'react';
import { Flex, Text, View } from '@adobe/react-spectrum';
import { get } from '../utils/api.js';

function NoMessages() {
  return <Text alignSelf="center">No messages</Text>;
}

export function ChatContent({
  channel = {},
  onLoad,
  currentUser,
  users = [],
  refreshTrigger = 0,
}) {
  const [messages, setMessages] = useState([]);

  useEffect(() => {
    if (!channel || !channel._id) {
      setMessages([]);
      return;
    }

    // Check if current user is a member of the channel
    const isUserMember = channel.memberIds?.includes(currentUser?._id) ?? false;

    // Only fetch messages if user is a member
    if (!isUserMember) {
      setMessages([]);
      return;
    }

    const fetchMessages = async () => {
      try {
        const response = await get(`/messages?channelId=${channel._id}`);
        if (!response.ok) {
          throw new Error('Failed to fetch messages');
        }
        const data = await response.json();
        setMessages(data);
        onLoad();
      } catch (error) {
        console.error('Error fetching messages:', error);
        setMessages([]);
      }
    };

    fetchMessages();
  }, [channel, channel?._id, currentUser?._id, refreshTrigger, onLoad]);

  return (
    <Flex
      direction="column-reverse"
      justifyContent="end"
      alignItems="start"
      gap="size-100"
      margin="size-200"
    >
      {messages.length === 0 ? (
        <NoMessages />
      ) : (
        messages.map((message) => {
          const isCurrentUser = message.senderId === currentUser?._id;
          const user = users.find((u) => u._id === message.senderId);
          const userName = user?.displayName;

          return (
            <Flex
              key={message._id}
              width="100%"
              justifyContent={isCurrentUser ? 'end' : 'start'}
            >
              <Flex direction="column" gap="size-50" maxWidth="70%">
                <Text
                  UNSAFE_style={{
                    fontSize: '12px',
                    color: '#666',
                    alignSelf: isCurrentUser ? 'flex-end' : 'flex-start',
                    paddingLeft: isCurrentUser ? '0' : '8px',
                    paddingRight: isCurrentUser ? '8px' : '0',
                  }}
                >
                  {userName}
                </Text>
                <View
                  backgroundColor={isCurrentUser ? 'blue-600' : 'gray-200'}
                  borderRadius="large"
                  paddingX="size-200"
                  paddingY="size-150"
                >
                  <Text
                    UNSAFE_style={{ color: isCurrentUser ? 'white' : 'black' }}
                  >
                    {message.content}
                  </Text>
                </View>
              </Flex>
            </Flex>
          );
        })
      )}
    </Flex>
  );
}
