import { useState, useEffect } from 'react';
import {
  Dialog,
  Heading,
  Divider,
  Content,
  TextField,
  TextArea,
  ListBox,
  Item,
  ButtonGroup,
  Button,
  Flex,
  Text,
  Form,
} from '@adobe/react-spectrum';
import { get, post } from '../utils/api.js';
import { getAuthUser } from '../utils/auth.js';

export function NewChannelDialog({ onChannelCreated, onCancel }) {
  const currentUser = getAuthUser();
  const [channelName, setChannelName] = useState('');
  const [channelDescription, setChannelDescription] = useState('');
  const [selectedUserIds, setSelectedUserIds] = useState(
    new Set(currentUser ? [currentUser._id] : [])
  );
  const [users, setUsers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  // Fetch users when component mounts
  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await get('/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      const data = await response.json();
      setUsers(data);
    } catch (error) {
      console.error('Error fetching users:', error);
      setError('Failed to load users');
    }
  };

  const handleCreateChannel = async () => {
    if (!channelName.trim()) {
      setError('Channel name is required');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await post('/channels', {
        name: channelName.trim(),
        description: channelDescription.trim(),
        userIds: Array.from(selectedUserIds),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create channel');
      }

      const newChannel = await response.json();

      // Reset form
      setChannelName('');
      setChannelDescription('');
      setSelectedUserIds(new Set(currentUser ? [currentUser._id] : []));

      // Notify parent
      if (onChannelCreated) {
        onChannelCreated(newChannel);
      }
    } catch (error) {
      console.error('Error creating channel:', error);
      setError(error.message || 'Failed to create channel');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setChannelName('');
    setChannelDescription('');
    setSelectedUserIds(new Set(currentUser ? [currentUser._id] : []));
    setError('');
    if (onCancel) {
      onCancel();
    }
  };

  return (
    <Dialog>
      <Heading>Create New Channel</Heading>
      <Divider />
      <Content>
        <Form>
          <Flex direction="column" gap="size-200">
            <TextField
              label="Channel Name"
              value={channelName}
              onChange={setChannelName}
              isRequired
              autoFocus
              placeholder="e.g., general, random"
              width="100%"
            />
            <TextArea
              label="Description"
              value={channelDescription}
              onChange={setChannelDescription}
              placeholder="What's this channel about?"
              width="100%"
            />
            <ListBox
              label="Add Members"
              aria-label="Select users to add to channel"
              items={users}
              selectionMode="multiple"
              selectedKeys={selectedUserIds}
              onSelectionChange={setSelectedUserIds}
              disabledKeys={currentUser ? [currentUser._id] : []}
              width="100%"
              height="size-2400"
            >
              {(user) => (
                <Item key={user._id} textValue={user.displayName}>
                  <Text>{user.displayName}</Text>
                </Item>
              )}
            </ListBox>
            {error && (
              <Text UNSAFE_style={{ color: 'red' }}>{error}</Text>
            )}
          </Flex>
        </Form>
      </Content>
      <ButtonGroup>
        <Button variant="secondary" onPress={handleCancel}>
          Cancel
        </Button>
        <Button
          variant="cta"
          onPress={handleCreateChannel}
          isDisabled={!channelName.trim() || isLoading}
        >
          {isLoading ? 'Creating...' : 'Create'}
        </Button>
      </ButtonGroup>
    </Dialog>
  );
}

