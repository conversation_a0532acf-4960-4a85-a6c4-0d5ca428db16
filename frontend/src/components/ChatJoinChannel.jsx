import { useState } from 'react';
import { Flex, But<PERSON>, Heading, Text, View } from '@adobe/react-spectrum';
import { post } from '../utils/api.js';

export function ChatJoinChannel({ channel, currentUser, onJoinSuccess }) {
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState('');

  const handleJoinChannel = async () => {
    if (!channel || !channel._id) {
      setError('No channel selected');
      return;
    }

    setIsJoining(true);
    setError('');

    try {
      const response = await post(`/channels/${channel._id}/join`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to join channel');
      }

      const updatedChannel = await response.json();

      // Notify parent component of successful join
      if (onJoinSuccess) {
        onJoinSuccess(updatedChannel);
      }
    } catch (error) {
      console.error('Error joining channel:', error);
      setError(error.message || 'Failed to join channel. Please try again.');
    } finally {
      setIsJoining(false);
    }
  };

  if (!channel) {
    return null;
  }

  return (
    <Flex
      direction="column"
      alignItems="center"
      justifyContent="center"
      height="100%"
      gap="size-200"
      padding="size-400"
    >
      <View
        padding="size-400"
        borderRadius="large"
        UNSAFE_style={{
          textAlign: 'center',
          maxWidth: '500px',
        }}
      >
        <Heading level={2} marginBottom="size-200">
          # {channel.name}
        </Heading>

        {channel.description && (
          <Text marginBottom="size-300">
            {channel.description}
          </Text>
        )}

        <View marginBottom="size-400">
          <Text>
            You are not a member of this channel. Join to view messages and participate in the conversation.
          </Text>
        </View>

        <Button
          variant="accent"
          onPress={handleJoinChannel}
          isDisabled={isJoining}
          UNSAFE_style={{
            minWidth: '150px',
          }}
        >
          {isJoining ? 'Joining...' : 'Join Channel'}
        </Button>

        {error && (
          <Text
            marginTop="size-200"
            UNSAFE_style={{
              color: 'var(--spectrum-global-color-red-600)',
            }}
          >
            {error}
          </Text>
        )}
      </View>
    </Flex>
  );
}

