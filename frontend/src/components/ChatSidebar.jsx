import { useState } from 'react';
import {
  Accordion,
  Disclosure,
  DisclosurePanel,
  DisclosureTitle,
  Flex,
  Text,
  Button,
  DialogTrigger,
} from '@adobe/react-spectrum';
import { NewChannelDialog } from './NewChannelDialog.jsx';

export function ChatSidebar({ channels, setChannel, onChannelCreated }) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const handleChannelCreated = (newChannel) => {
    setIsDialogOpen(false);
    if (onChannelCreated) {
      onChannelCreated(newChannel);
    }
  };

  const handleCancel = () => {
    setIsDialogOpen(false);
  };

  return (
    <Accordion defaultExpandedKeys={['channels']} flexGrow={1}>
      <Disclosure id="channels">
        <DisclosureTitle>Channels</DisclosureTitle>
        <DisclosurePanel>
          <Flex direction="column" alignItems="stretch" gap="size-100">
            <DialogTrigger isOpen={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <Button variant="primary" width="100%">
                Create Channel
              </Button>
              <NewChannelDialog
                onChannelCreated={handleChannelCreated}
                onCancel={handleCancel}
              />
            </DialogTrigger>

            {channels.map((channel) => (
              <div
                key={channel._id}
                onClick={() => setChannel(channel)}
                style={{ cursor: 'pointer' }}
              >
                <Text level={3}>{channel.name}</Text>
              </div>
            ))}
          </Flex>
        </DisclosurePanel>
      </Disclosure>
    </Accordion>
  );
}
