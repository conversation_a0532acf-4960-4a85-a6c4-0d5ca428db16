import {
  View,
  Flex,
  Avatar,
  Text,
  Button,
} from '@adobe/react-spectrum';
import { useNavigate } from 'react-router-dom';
import { logout } from '../utils/auth.js';


export function Settings({ currentUser }) {
  const navigate = useNavigate();

  if (!currentUser) {
    return null;
  }

  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  return (
    <View
      padding="size-200"
      borderTopWidth="thin"
      borderTopColor="gray-300"
      width="100%"
    >
      <Flex direction="column" gap="size-150" width="100%">
        <Flex direction="row" alignItems="center" gap="size-150">
          <Avatar
            src={currentUser.avatar}
            alt={currentUser.displayName}
            size="avatar-size-400"
          />
          <Flex direction="column" gap="size-50" flex={1}>
            <Text UNSAFE_style={{ fontWeight: 'bold', fontSize: '14px' }}>
              {currentUser.displayName}
            </Text>
          </Flex>
        </Flex>
        <Button variant="secondary" onPress={handleLogout} width="100%">
          Logout
        </Button>
      </Flex>
    </View>
  );
}
