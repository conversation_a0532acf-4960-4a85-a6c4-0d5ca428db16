import { useState } from 'react';
import { Flex, TextArea, Button, View } from '@adobe/react-spectrum';

export function ChatMessager({ onSend }) {
  const [message, setMessage] = useState('');

  const handleSend = () => {
    if (message.trim()) {
      onSend(message);
      setMessage('');
    }
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <View
      padding="size-200"
      UNSAFE_style={{
        borderRadius: '8px',
        border: '1px solid #ccc',
        backgroundColor: '#fff',
        margin: '12px',
      }}
    >
      <Flex direction="row" gap="size-100" alignItems="end">
        <View flex={1}>
          <TextArea
            label="Message"
            value={message}
            onChange={setMessage}
            width="100%"
            onKeyDown={handleKeyDown}
            UNSAFE_style={{
              minHeight: '44px',
              resize: 'vertical',
            }}
          />
        </View>
        <Button
          variant="accent"
          onPress={handleSend}
          isDisabled={!message.trim()}
          UNSAFE_style={{
            marginBottom: '4px',
          }}
        >
          Send
        </Button>
      </Flex>
    </View>
  );
}
