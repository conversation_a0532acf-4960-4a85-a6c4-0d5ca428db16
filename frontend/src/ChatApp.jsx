import { useState, useRef, useEffect } from 'react';
import './ChatApp.css';
import {
  Flex,
  Heading,
  View,
  ProgressCircle,
} from '@adobe/react-spectrum';
import { ChatHeader } from './components/ChatHeader.jsx';
import { ChatSidebar } from './components/ChatSidebar.jsx';
import { ChatContent } from './components/ChatContent.jsx';
import { ChatMessager } from './components/ChatMessager.jsx';
import { ChatJoinChannel } from './components/ChatJoinChannel.jsx';
import { getAuthUser } from './utils/auth.js';
import { get, post } from './utils/api.js';
import { Settings } from './components/Settings.jsx'

export function ChatApp() {
  const [channels, setChannels] = useState([]);
  const [users, setUsers] = useState([]);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [messageRefreshTrigger, setMessageRefreshTrigger] = useState(0);
  const contentRef = useRef(null);
  const authUser = getAuthUser();

  const scrollToBottom = () => {
    if (contentRef.current) {
      setTimeout(() => {
        contentRef.current.UNSAFE_getDOMNode().scrollTop =
          contentRef.current.UNSAFE_getDOMNode().scrollHeight;
      }, 50);
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [channelsRes, usersRes] = await Promise.all([
          get('/channels'),
          get('/users'),
        ]);

        if (!channelsRes.ok || !usersRes.ok) {
          throw new Error('Failed to fetch data');
        }

        const channelsData = await channelsRes.json();
        const usersData = await usersRes.json();

        setChannels(channelsData);
        setUsers(usersData);

        if (channelsData.length > 0) {
          setSelectedChannel(channelsData[0]);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const onSend = async (channelId, message) => {
    try {
      const response = await post('/messages', {
        content: message,
        senderId: authUser._id,
        channelId: selectedChannel._id,
      });

      if (!response.ok) {
        throw new Error('Failed to send message');
      }

      setMessageRefreshTrigger((prev) => prev + 1);
      scrollToBottom();
    } catch (error) {
      console.error('Error sending message:', error);
      alert('Failed to send message. Please try again.');
    }
  };

  const handleChannelCreated = async (newChannel) => {
    try {
      // Refresh the channels list
      const response = await get('/channels');
      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }
      const channelsData = await response.json();
      setChannels(channelsData);

      // Select the newly created channel
      setSelectedChannel(newChannel);
    } catch (error) {
      console.error('Error refreshing channels:', error);
    }
  };

  const handleJoinSuccess = async (updatedChannel) => {
    try {
      setSelectedChannel(updatedChannel);

      const response = await get('/channels');
      if (!response.ok) {
        throw new Error('Failed to fetch channels');
      }
      const channelsData = await response.json();
      setChannels(channelsData);

      setMessageRefreshTrigger((prev) => prev + 1);
    } catch (error) {
      console.error('Error refreshing after join:', error);
    }
  };

  const currentUser = authUser || users[0];

  // Check if current user is a member of the selected channel
  const isUserMember = selectedChannel?.memberIds?.includes(currentUser?._id) ?? false;

  if (loading) {
    return (
      <Flex
        direction="column"
        alignItems="center"
        justifyContent="center"
        height="100vh"
        width="100vw"
      >
        <ProgressCircle aria-label="Loading..." isIndeterminate />
      </Flex>
    );
  }

  if (error) {
    return (
      <Flex
        direction="column"
        alignItems="center"
        justifyContent="center"
        height="100vh"
        width="100vw"
      >
        <Heading level={2}>Error loading data</Heading>
        <p>{error}</p>
        <p>Make sure the backend server is running on http://localhost:3000</p>
      </Flex>
    );
  }

  return (
    <Flex
      direction="row"
      height="100vh"
      width="100vw"
      alignItems="stretch"
      justifyContent="stretch"
    >
      <Flex
        direction="column"
        UNSAFE_style={{
          backgroundColor: 'var(--spectrum-global-color-static-blue-200)',
        }}
      >
        <ChatHeader>Header</ChatHeader>
        <ChatSidebar
          channels={channels}
          setChannel={setSelectedChannel}
          currentUser={currentUser}
          onChannelCreated={handleChannelCreated}
        />
        <Settings currentUser={currentUser} />
      </Flex>
      <Flex direction="column" height="100%" flexGrow={1}>
        <View
          padding="size-200"
          borderBottomWidth="thin"
          borderBottomColor="gray-300"
          UNSAFE_style={{ flexShrink: 0 }}
        >
          <Heading level={3} margin={0}>
            {selectedChannel?.name
              ? `# ${selectedChannel.name}`
              : 'Select a channel'}
          </Heading>
        </View>
        <View
          ref={contentRef}
          flex={1}
          overflow="auto"
          UNSAFE_style={{ minHeight: 0 }}
        >
          {isUserMember ? (
            <ChatContent
              channel={selectedChannel}
              onLoad={scrollToBottom}
              currentUser={currentUser}
              users={users}
              refreshTrigger={messageRefreshTrigger}
            />
          ) : (
            <ChatJoinChannel
              channel={selectedChannel}
              currentUser={currentUser}
              onJoinSuccess={handleJoinSuccess}
            />
          )}
        </View>
        {isUserMember && (
          <View UNSAFE_style={{ flexShrink: 0 }}>
            <ChatMessager
              onSend={(message) => onSend(selectedChannel?._id, message)}
            />
          </View>
        )}
      </Flex>
    </Flex>
  );
}
