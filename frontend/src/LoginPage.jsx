import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  View,
  Flex,
  Form,
  TextField,
  Button,
  Heading,
  Text,
} from '@adobe/react-spectrum';
import { API_BASE_URL } from './config/api';
import { setAuthToken, setAuthUser } from './utils/auth';

export function LoginPage() {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleLogin = async (e) => {
    e.preventDefault();
    setError('');
    setIsLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username || undefined,
          email: username.includes('@') ? username : undefined,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Login failed');
        setIsLoading(false);
        return;
      }

      // Store token and user
      setAuthToken(data.token);
      setAuthUser(data.user);

      // Navigate to app
      navigate('/app');
    } catch (error) {
      setError('Network error. Please try again.');
      setIsLoading(false);
      console.log(error);
    }
  };

  return (
    <Flex
      width="100vw"
      height="100vh"
      backgroundColor="gray-50"
      alignItems="center"
      justifyContent="center"
    >
      <Flex
        direction="column"
        gap="size-300"
        width="size-3000"
        padding="size-400"
        backgroundColor="white"
        borderRadius="medium"
        boxShadow="sm"
      >
        <Heading level={1} size="L">
          ChatterBox
        </Heading>
        <Text>Sign in to your account</Text>

        <Form onSubmit={handleLogin}>
          <Flex direction="column" gap="size-200">
            <TextField
              label="Username or Email"
              value={username}
              onChange={setUsername}
              isRequired
              placeholder="Enter your username or email"
            />

            <TextField
              label="Password"
              type="password"
              value={password}
              onChange={setPassword}
              isRequired
              placeholder="Enter your password"
            />

            {error && (
              <Text color="negative" size="S">
                {error}
              </Text>
            )}

            <Button
              type="submit"
              variant="cta"
              isDisabled={isLoading}
              width="100%"
            >
              {isLoading ? 'Signing in...' : 'Sign In'}
            </Button>
          </Flex>
        </Form>

        <Flex direction="column" gap="size-100" alignItems="center">
          <Text size="S">Don't have an account?</Text>
          <Button
            variant="secondary"
            onPress={() => navigate('/register')}
            width="100%"
          >
            Register
          </Button>
        </Flex>

        <Text size="S" color="gray">
          Demo credentials
          <br />
          username: sarah.johnson
          <br />
          password: password123
        </Text>
      </Flex>
    </Flex>
  );
}
