import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Flex,
  Form,
  TextField,
  Button,
  Heading,
  Text,
} from '@adobe/react-spectrum';
import { API_BASE_URL } from './config/api';
import { setAuthToken, setAuthUser } from './utils/auth';

export function RegisterPage() {
  const [username, setUsername] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const navigate = useNavigate();

  const handleRegister = async (e) => {
    e.preventDefault();
    setError('');

    if (!username || !email || !password || !confirmPassword) {
      setError('All fields are required');
      return;
    }

    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch(`${API_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username,
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.message || data.error || 'Registration failed');
        setIsLoading(false);
        return;
      }

      // Store token and user (automatically log in after registration)
      setAuthToken(data.token);
      setAuthUser(data.user);

      navigate('/app');
    } catch (error) {
      setError('Network error. Please try again.');
      setIsLoading(false);
      console.log(error)
    }
  };

  return (
    <Flex
      width="100vw"
      height="100vh"
      backgroundColor="gray-50"
      alignItems="center"
      justifyContent="center"
    >
      <Flex
        direction="column"
        gap="size-300"
        width="size-3000"
        padding="size-400"
        backgroundColor="white"
        borderRadius="medium"
        boxShadow="sm"
      >
        <Heading level={1} size="L">
          ChatterBox
        </Heading>
        <Text>Create your account</Text>

        <Form onSubmit={handleRegister}>
          <Flex direction="column" gap="size-200">
            <TextField
              label="Username"
              value={username}
              onChange={setUsername}
              isRequired
              placeholder="Choose a username"
            />

            <TextField
              label="Email"
              type="email"
              value={email}
              onChange={setEmail}
              isRequired
              placeholder="Enter your email"
            />

            <TextField
              label="Password"
              type="password"
              value={password}
              onChange={setPassword}
              isRequired
              placeholder="Choose a password"
            />

            <TextField
              label="Confirm Password"
              type="password"
              value={confirmPassword}
              onChange={setConfirmPassword}
              isRequired
              placeholder="Confirm your password"
            />

            {error && (
              <Text color="negative" size="S">
                {error}
              </Text>
            )}

            <Button
              type="submit"
              variant="cta"
              isDisabled={isLoading}
              width="100%"
            >
              {isLoading ? 'Creating account...' : 'Register'}
            </Button>
          </Flex>
        </Form>

        <Flex direction="column" gap="size-100" alignItems="center">
          <Text size="S">Already have an account?</Text>
          <Button
            variant="secondary"
            onPress={() => navigate('/login')}
            width="100%"
          >
            Sign In
          </Button>
        </Flex>
      </Flex>
    </Flex>
  );
}
