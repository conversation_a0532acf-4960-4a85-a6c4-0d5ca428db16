import { API_BASE_URL } from '../config/api.js';
import { logout, getAuthToken } from './auth.js';

/**
 * Custom fetch wrapper that handles 401 (Unauthorized) responses
 * automatically by clearing auth data and redirecting to login.
 *
 * @param {string} url - The URL to fetch (can be relative to API_BASE_URL or absolute)
 * @param {RequestInit} options - Fetch options
 * @returns {Promise<Response>} - The fetch response
 */
export async function authenticatedFetch(url, options = {}) {
  const fullUrl = url.startsWith('http') ? url : `${API_BASE_URL}${url.startsWith('/') ? url : `/${url}`}`;

  const token = getAuthToken();
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  try {
    const response = await fetch(fullUrl, {
      ...options,
      headers,
    });

    // Check for 401 Unauthorized response
    if (response.status === 401) {
      console.warn('401 Unauthorized - Session expired or invalid. Redirecting to login...');

      // Clear authentication data
      logout();

      window.location.href = '/login';

      throw new Error('Unauthorized - Session expired');
    }

    return response;
  } catch (error) {
    // If it's a network error or our 401 error, rethrow it
    console.log(error);
    throw error;
  }
}

/**
 * Convenience method for GET requests
 * @param {string} url - The URL to fetch
 * @param {RequestInit} options - Additional fetch options
 * @returns {Promise<Response>}
 */
export async function get(url, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'GET',
  });
}

/**
 * Convenience method for POST requests
 * @param {string} url - The URL to fetch
 * @param {any} data - The data to send in the request body
 * @param {RequestInit} options - Additional fetch options
 * @returns {Promise<Response>}
 */
export async function post(url, data, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'POST',
    body: JSON.stringify(data),
  });
}

/**
 * Convenience method for PUT requests
 * @param {string} url - The URL to fetch
 * @param {any} data - The data to send in the request body
 * @param {RequestInit} options - Additional fetch options
 * @returns {Promise<Response>}
 */
export async function put(url, data, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'PUT',
    body: JSON.stringify(data),
  });
}

/**
 * Convenience method for DELETE requests
 * @param {string} url - The URL to fetch
 * @param {RequestInit} options - Additional fetch options
 * @returns {Promise<Response>}
 */
export async function del(url, options = {}) {
  return authenticatedFetch(url, {
    ...options,
    method: 'DELETE',
  });
}

