export function setAuthToken(token) {
  localStorage.setItem('authToken', token);
}

export function getAuthToken() {
  return localStorage.getItem('authToken');
}

export function removeAuthToken() {
  localStorage.removeItem('authToken');
}

export function setAuthUser(user) {
  localStorage.setItem('authUser', JSON.stringify(user));
}

export function getAuthUser() {
  const user = localStorage.getItem('authUser');
  return user ? JSON.parse(user) : null;
}

export function removeAuthUser() {
  localStorage.removeItem('authUser');
}

export function isAuthenticated() {
  return !!getAuthToken();
}

export function logout() {
  removeAuthToken();
  removeAuthUser();
}
