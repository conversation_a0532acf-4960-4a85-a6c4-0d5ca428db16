const { MongoClient } = require('mongodb');
const bcrypt = require('bcryptjs');

// Get connection string from command line
const connectionString = process.argv[2];

if (!connectionString) {
  console.error('Error: Please provide a MongoDB connection string');
  console.error('Usage: node insert-data.js MONGO_CONNECTION_STRING');
  process.exit(1);
}

// Data from DUMMY_DATA.json
// Note: passwords will be hashed during insertion
const users = [
  {
    _id: "user-001",
    username: "sarah.johnson",
    email: "<EMAIL>",
    password: "password123", // Will be hashed
  },
  {
    _id: "user-002",
    username: "mike.chen",
    email: "<EMAIL>",
    password: "password123", // Will be hashed
  },
  {
    _id: "user-003",
    username: "emily.rodriguez",
    email: "<EMAIL>",
    password: "password123", // Will be hashed
  },
  {
    _id: "user-004",
    username: "james.wilson",
    email: "<EMAIL>",
    password: "password123", // Will be hashed
  },
  {
    _id: "user-005",
    username: "lisa.patel",
    email: "<EMAIL>",
    password: "password123", // Will be hashed
  }
];

const channels = [
  {
    _id: "channel-001",
    name: "general",
    description: "Company-wide announcements and general discussion",
    createdBy: "user-001",
    memberIds: ["user-001", "user-002", "user-003", "user-004", "user-005"]
  },
  {
    _id: "channel-002",
    name: "random",
    description: "Non-work banter and water cooler conversation",
    createdBy: "user-001",
    memberIds: ["user-001", "user-002", "user-003", "user-004", "user-005"]
  },
  {
    _id: "channel-003",
    name: "engineering",
    description: "Engineering team discussions and technical topics",
    createdBy: "user-002",
    memberIds: ["user-001", "user-002", "user-005"]
  },
  {
    _id: "channel-004",
    name: "design",
    description: "Design team collaboration and feedback",
    createdBy: "user-003",
    memberIds: ["user-001", "user-003", "user-004"]
  },
  {
    _id: "channel-005",
    name: "project-alpha",
    description: "Private channel for Project Alpha team",
    createdBy: "user-001",
    memberIds: ["user-001", "user-002", "user-005"]
  },
  {
    _id: "channel-006",
    name: "announcements",
    description: "Important company announcements",
    createdBy: "user-001",
    memberIds: ["user-001", "user-002", "user-003", "user-004", "user-005"]
  }
];

const messages = [
  {
    _id: "msg-001",
    channelId: "channel-001",
    senderId: "user-002",
    content: "Good morning team! Ready for the sprint planning meeting?",
    timestamp: "2025-10-25T09:00:00Z"
  },
  {
    _id: "msg-002",
    channelId: "channel-001",
    senderId: "user-001",
    content: "Absolutely! I've prepared the agenda. Let's meet in 10 minutes.",
    timestamp: "2025-10-25T09:02:00Z"
  },
  {
    _id: "msg-003",
    channelId: "channel-001",
    senderId: "user-005",
    content: "Just pushed the latest changes to the repo. The build is passing! 🎉",
    timestamp: "2025-10-25T14:20:00Z"
  },
  {
    _id: "msg-004",
    channelId: "channel-002",
    senderId: "user-003",
    content: "Anyone want to grab lunch? Thinking of trying that new taco place 🌮",
    timestamp: "2025-10-25T11:45:00Z"
  },
  {
    _id: "msg-005",
    channelId: "channel-002",
    senderId: "user-004",
    content: "I'm in! What time?",
    timestamp: "2025-10-25T11:47:00Z"
  },
  {
    _id: "msg-006",
    channelId: "channel-002",
    senderId: "user-003",
    content: "12:30 works for me",
    timestamp: "2025-10-25T12:45:00Z"
  },
  {
    _id: "msg-007",
    channelId: "channel-003",
    senderId: "user-002",
    content: "We need to discuss the API architecture for the new feature. I've created a design doc:",
    timestamp: "2025-10-25T10:15:00Z"
  },
  {
    _id: "msg-008",
    channelId: "channel-003",
    senderId: "user-005",
    content: "Looking at the performance metrics from last week. We've improved load time by 40%! 📊",
    timestamp: "2025-10-25T14:15:00Z"
  },
  {
    _id: "msg-009",
    channelId: "channel-004",
    senderId: "user-003",
    content: "New mockups for the dashboard redesign. Feedback welcome!",
    timestamp: "2025-10-25T11:30:00Z"
  },
  {
    _id: "msg-010",
    channelId: "channel-006",
    senderId: "user-001",
    content: "📢 Reminder: All-hands meeting tomorrow at 2 PM EST. Please review the quarterly results beforehand.",
    timestamp: "2025-10-24T16:00:00Z"
  },
  {
    _id: "msg-011",
    channelId: "dm-001",
    senderId: "user-002",
    content: "Hey Sarah, can we sync up about the code review? I have some questions.",
    timestamp: "2025-10-25T13:30:00Z"
  },
  {
    _id: "msg-012",
    channelId: "dm-001",
    senderId: "user-001",
    content: "Sure! I'm free in 15 minutes. Want to hop on a quick call?",
    timestamp: "2025-10-25T13:45:00Z"
  },
  {
    _id: "msg-013",
    channelId: "dm-002",
    senderId: "user-003",
    content: "The design assets are ready for the landing page. I'll share the Figma link.",
    timestamp: "2025-10-25T14:05:00Z"
  },
  {
    _id: "msg-014",
    channelId: "dm-002",
    senderId: "user-003",
    content: "https://figma.com/file/example-design-link",
    timestamp: "2025-10-25T14:10:00Z"
  },
  {
    _id: "msg-015",
    channelId: "channel-001",
    senderId: "user-003",
    content: "Has anyone seen the latest product roadmap? Looks exciting!",
    timestamp: "2025-10-25T08:15:00Z"
  },
  {
    _id: "msg-016",
    channelId: "channel-001",
    senderId: "user-004",
    content: "Morning everyone! Coffee is ready in the kitchen ☕",
    timestamp: "2025-10-25T08:30:00Z"
  },
  {
    _id: "msg-017",
    channelId: "channel-001",
    senderId: "user-005",
    content: "Quick reminder: code freeze starts tomorrow for the release",
    timestamp: "2025-10-25T10:00:00Z"
  },
  {
    _id: "msg-018",
    channelId: "channel-001",
    senderId: "user-002",
    content: "The deployment went smoothly last night. No issues reported so far.",
    timestamp: "2025-10-25T10:30:00Z"
  },
  {
    _id: "msg-019",
    channelId: "channel-001",
    senderId: "user-001",
    content: "Great job team! Let's keep up the momentum 💪",
    timestamp: "2025-10-25T11:00:00Z"
  },
  {
    _id: "msg-020",
    channelId: "channel-002",
    senderId: "user-005",
    content: "Who's watching the game tonight? 🏀",
    timestamp: "2025-10-25T13:00:00Z"
  },
  {
    _id: "msg-021",
    channelId: "channel-002",
    senderId: "user-002",
    content: "I am! Should be a good one",
    timestamp: "2025-10-25T13:05:00Z"
  },
  {
    _id: "msg-022",
    channelId: "channel-002",
    senderId: "user-001",
    content: "Just finished reading an amazing sci-fi novel. Any recommendations for the next one?",
    timestamp: "2025-10-25T13:30:00Z"
  },
  {
    _id: "msg-023",
    channelId: "channel-002",
    senderId: "user-004",
    content: "Try 'Project Hail Mary' if you haven't already!",
    timestamp: "2025-10-25T13:35:00Z"
  },
  {
    _id: "msg-024",
    channelId: "channel-002",
    senderId: "user-003",
    content: "Happy Friday everyone! Any fun weekend plans? 🎉",
    timestamp: "2025-10-25T14:00:00Z"
  },
  {
    _id: "msg-025",
    channelId: "channel-002",
    senderId: "user-005",
    content: "Hiking trip! Weather looks perfect 🏔️",
    timestamp: "2025-10-25T14:05:00Z"
  },
  {
    _id: "msg-026",
    channelId: "channel-003",
    senderId: "user-001",
    content: "Starting code review for PR #234. Should be done by EOD.",
    timestamp: "2025-10-25T09:30:00Z"
  },
  {
    _id: "msg-027",
    channelId: "channel-003",
    senderId: "user-005",
    content: "Thanks! I addressed the comments from yesterday's review.",
    timestamp: "2025-10-25T09:45:00Z"
  },
  {
    _id: "msg-028",
    channelId: "channel-003",
    senderId: "user-002",
    content: "Database migration scripts are ready for review. Please check before we run them in staging.",
    timestamp: "2025-10-25T11:00:00Z"
  },
  {
    _id: "msg-029",
    channelId: "channel-003",
    senderId: "user-001",
    content: "Looks good to me. Let's run it in staging first thing Monday morning.",
    timestamp: "2025-10-25T11:30:00Z"
  },
  {
    _id: "msg-030",
    channelId: "channel-003",
    senderId: "user-005",
    content: "I've updated the CI/CD pipeline to include the new security checks",
    timestamp: "2025-10-25T12:00:00Z"
  },
  {
    _id: "msg-031",
    channelId: "channel-003",
    senderId: "user-002",
    content: "The new authentication service is now live in production! 🚀",
    timestamp: "2025-10-25T13:00:00Z"
  },
  {
    _id: "msg-032",
    channelId: "channel-003",
    senderId: "user-001",
    content: "Monitoring looks good. Response times are under 100ms consistently.",
    timestamp: "2025-10-25T13:30:00Z"
  },
  {
    _id: "msg-033",
    channelId: "channel-003",
    senderId: "user-005",
    content: "Should we schedule a post-mortem for the incident last week?",
    timestamp: "2025-10-25T14:00:00Z"
  },
  {
    _id: "msg-034",
    channelId: "channel-004",
    senderId: "user-001",
    content: "The new logo variations look fantastic!",
    timestamp: "2025-10-25T09:00:00Z"
  },
  {
    _id: "msg-035",
    channelId: "channel-004",
    senderId: "user-004",
    content: "Working on the mobile app icons today. Will share progress later.",
    timestamp: "2025-10-25T09:30:00Z"
  },
  {
    _id: "msg-036",
    channelId: "channel-004",
    senderId: "user-003",
    content: "Can someone review the accessibility guidelines I drafted?",
    timestamp: "2025-10-25T10:00:00Z"
  },
  {
    _id: "msg-037",
    channelId: "channel-004",
    senderId: "user-001",
    content: "I'll take a look this afternoon!",
    timestamp: "2025-10-25T10:15:00Z"
  },
  {
    _id: "msg-038",
    channelId: "channel-004",
    senderId: "user-004",
    content: "The color contrast ratios all pass WCAG AA standards now ✅",
    timestamp: "2025-10-25T10:45:00Z"
  },
  {
    _id: "msg-039",
    channelId: "channel-004",
    senderId: "user-003",
    content: "Updated the design system documentation with the new components",
    timestamp: "2025-10-25T11:00:00Z"
  },
  {
    _id: "msg-040",
    channelId: "channel-005",
    senderId: "user-001",
    content: "Project Alpha timeline update: we're on track for the Q2 launch 🎯",
    timestamp: "2025-10-25T08:00:00Z"
  },
  {
    _id: "msg-041",
    channelId: "channel-005",
    senderId: "user-002",
    content: "The beta testing group has been finalized. 50 users selected.",
    timestamp: "2025-10-25T08:30:00Z"
  },
  {
    _id: "msg-042",
    channelId: "channel-005",
    senderId: "user-005",
    content: "Marketing materials are ready for review. Need approval by next week.",
    timestamp: "2025-10-25T09:00:00Z"
  },
  {
    _id: "msg-043",
    channelId: "channel-005",
    senderId: "user-001",
    content: "I'll review them today. Can you send me the link?",
    timestamp: "2025-10-25T09:15:00Z"
  },
  {
    _id: "msg-044",
    channelId: "channel-005",
    senderId: "user-002",
    content: "Legal has approved the terms of service. We're good to go!",
    timestamp: "2025-10-25T10:00:00Z"
  },
  {
    _id: "msg-045",
    channelId: "channel-006",
    senderId: "user-001",
    content: "🎉 Congratulations to the team for hitting our Q3 targets!",
    timestamp: "2025-10-24T10:00:00Z"
  },
  {
    _id: "msg-046",
    channelId: "channel-006",
    senderId: "user-001",
    content: "New office hours: We're now open 24/7 for remote workers in all time zones",
    timestamp: "2025-10-24T12:00:00Z"
  },
  {
    _id: "msg-047",
    channelId: "channel-006",
    senderId: "user-001",
    content: "📢 Company holiday party scheduled for December 15th. Save the date!",
    timestamp: "2025-10-24T14:00:00Z"
  },
  {
    _id: "msg-048",
    channelId: "dm-001",
    senderId: "user-001",
    content: "Thanks for the quick turnaround on that bug fix!",
    timestamp: "2025-10-25T14:00:00Z"
  },
  {
    _id: "msg-049",
    channelId: "dm-001",
    senderId: "user-002",
    content: "No problem! Let me know if you need anything else.",
    timestamp: "2025-10-25T14:05:00Z"
  },
  {
    _id: "msg-050",
    channelId: "dm-002",
    senderId: "user-001",
    content: "Perfect! I'll start implementing this today.",
    timestamp: "2025-10-25T14:15:00Z"
  },
  {
    _id: "msg-051",
    channelId: "dm-002",
    senderId: "user-003",
    content: "Great! Let me know if you have any questions about the designs.",
    timestamp: "2025-10-25T14:20:00Z"
  },
  {
    _id: "msg-052",
    channelId: "dm-003",
    senderId: "user-001",
    content: "Hey Lisa, do you have time for a quick sync about the API integration?",
    timestamp: "2025-10-25T14:30:00Z"
  },
  {
    _id: "msg-053",
    channelId: "dm-003",
    senderId: "user-005",
    content: "Sure! I'm free in 10 minutes. Want to jump on a call?",
    timestamp: "2025-10-25T14:35:00Z"
  },
  {
    _id: "msg-054",
    channelId: "channel-001",
    senderId: "user-004",
    content: "Don't forget to submit your timesheets by EOD today!",
    timestamp: "2025-10-25T15:00:00Z"
  },
  {
    _id: "msg-055",
    channelId: "channel-002",
    senderId: "user-002",
    content: "Anyone interested in a team game night next Friday? 🎮",
    timestamp: "2025-10-25T15:15:00Z"
  },
  {
    _id: "msg-056",
    channelId: "channel-003",
    senderId: "user-002",
    content: "Updated the tech stack documentation. Please review when you get a chance.",
    timestamp: "2025-10-25T15:30:00Z"
  },
  {
    _id: "msg-057",
    channelId: "channel-004",
    senderId: "user-003",
    content: "Final mockups for the onboarding flow are ready! 🎨",
    timestamp: "2025-10-25T15:45:00Z"
  },
  {
    _id: "msg-058",
    channelId: "channel-001",
    senderId: "user-005",
    content: "Server maintenance scheduled for Sunday 2 AM - 4 AM EST",
    timestamp: "2025-10-25T16:00:00Z"
  },
  {
    _id: "msg-059",
    channelId: "channel-003",
    senderId: "user-001",
    content: "Code coverage is now at 87%! Great work everyone 📈",
    timestamp: "2025-10-25T16:15:00Z"
  },
  {
    _id: "msg-060",
    channelId: "channel-002",
    senderId: "user-004",
    content: "Just discovered this amazing coffee shop downtown. Highly recommend! ☕",
    timestamp: "2025-10-25T16:30:00Z"
  },
  {
    _id: "msg-061",
    channelId: "channel-005",
    senderId: "user-005",
    content: "Beta feedback is overwhelmingly positive! 94% satisfaction rate 🌟",
    timestamp: "2025-10-25T16:45:00Z"
  },
  {
    _id: "msg-062",
    channelId: "channel-001",
    senderId: "user-002",
    content: "Team standup in 5 minutes! See you all there 👋",
    timestamp: "2025-10-25T17:00:00Z"
  },
  {
    _id: "msg-063",
    channelId: "channel-003",
    senderId: "user-005",
    content: "Docker images have been optimized. Build time reduced by 60%! 🐳",
    timestamp: "2025-10-25T17:15:00Z"
  },
  {
    _id: "msg-064",
    channelId: "channel-004",
    senderId: "user-004",
    content: "Animation prototypes are ready for the loading states",
    timestamp: "2025-10-25T17:30:00Z"
  }
];

async function insertData() {
  const client = new MongoClient(connectionString);

  try {
    console.log('Connecting to MongoDB...');
    await client.connect();
    console.log('Connected successfully!');

    const db = client.db();

    // Hash passwords for all users
    console.log('\nHashing passwords...');
    const usersWithHashedPasswords = await Promise.all(
      users.map(async (user) => ({
        ...user,
        password: await bcrypt.hash(user.password, 10),
      }))
    );

    // Insert users
    console.log('\nInserting users...');
    const usersCollection = db.collection('users');
    await usersCollection.deleteMany({}); // Clear existing data
    const usersResult = await usersCollection.insertMany(usersWithHashedPasswords);
    console.log(`✓ Inserted ${usersResult.insertedCount} users`);

    // Insert channels
    console.log('\nInserting channels...');
    const channelsCollection = db.collection('channels');
    await channelsCollection.deleteMany({}); // Clear existing data
    const channelsResult = await channelsCollection.insertMany(channels);
    console.log(`✓ Inserted ${channelsResult.insertedCount} channels`);

    // Insert messages
    console.log('\nInserting messages...');
    const messagesCollection = db.collection('messages');
    await messagesCollection.deleteMany({}); // Clear existing data
    const messagesResult = await messagesCollection.insertMany(messages);
    console.log(`✓ Inserted ${messagesResult.insertedCount} messages`);

    console.log('\n✅ All data inserted successfully!');
    console.log('\nSummary:');
    console.log(`  - Users: ${usersResult.insertedCount}`);
    console.log(`  - Channels: ${channelsResult.insertedCount}`);
    console.log(`  - Messages: ${messagesResult.insertedCount}`);

  } catch (error) {
    console.error('Error inserting data:', error);
    process.exit(1);
  } finally {
    await client.close();
    console.log('\nConnection closed.');
  }
}

// Run the insertion
insertData();


